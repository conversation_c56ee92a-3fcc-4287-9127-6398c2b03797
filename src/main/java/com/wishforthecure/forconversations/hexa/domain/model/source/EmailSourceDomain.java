package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
public class EmailSourceDomain implements SourceDomain {

    SourceDomainId sourceId;
    Instant time;
    List<EmailMessageDomain> messages;
    byte[] source;
    SourceDomainType sourceType = SourceDomainType.EMAIL;

    @Override
    public SourceDomainId getSourceId() {
        return sourceId;
    }

    @Override
    public Instant getTime() {
        return time;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MessageDomain> getMessages() {
        return (List<MessageDomain>) (List<?>) messages;
    }

    public List<EmailMessageDomain> getEmailMessages() {
        return messages;
    }

    @Override
    public byte[] getSource() {
        return source;
    }

    public static EmailSourceDomain create(SourceDomainId sourceId, Instant time, List<EmailMessageDomain> messages, byte[] source) {
        return new EmailSourceDomain(sourceId, time, messages, source, SourceDomainType.EMAIL);
    }
}
