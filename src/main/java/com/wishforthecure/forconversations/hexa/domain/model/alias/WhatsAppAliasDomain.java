package com.wishforthecure.forconversations.hexa.domain.model.alias;

import lombok.AllArgsConstructor;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
@AllArgsConstructor(staticName = "create")
public class WhatsAppAliasDomain implements AliasDomain {

    private final AliasDomainId id;
    private final String name;
    private final String mobile;

    @Override
    public AliasDomainId getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getValue() {
        return mobile;
    }
}
