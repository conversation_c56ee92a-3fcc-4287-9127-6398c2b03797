package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.AudioMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
public class AudioSourceDomain implements SourceDomain {

    SourceDomainId sourceId;
    Instant time;
    List<AudioMessageDomain> messages;
    byte[] source;
    SourceDomainType sourceType = SourceDomainType.WHATSAPP_AUDIO;

    @Override
    public SourceDomainId getSourceId() {
        return sourceId;
    }

    @Override
    public Instant getTime() {
        return time;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MessageDomain> getMessages() {
        return (List<MessageDomain>) (List<?>) messages;
    }

    public List<AudioMessageDomain> getWhatsAppAudioMessages() {
        return messages;
    }

    @Override
    public byte[] getSource() {
        return source;
    }

    public static AudioSourceDomain create(SourceDomainId sourceId, Instant time, List<AudioMessageDomain> messages, byte[] source) {
        return new AudioSourceDomain(sourceId, time, messages, source, SourceDomainType.WHATSAPP_AUDIO);
    }
}
