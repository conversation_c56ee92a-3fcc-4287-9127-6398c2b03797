package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import java.time.Instant;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class DateRangeFilter implements MessageFilter {

    Instant startDate;
    Instant endDate;
    FilterMode mode;

    @Override
    public boolean apply(MessageDomain message) {
        Instant time = message.time();
        boolean withinRange = !time.isBefore(startDate) && !time.isAfter(endDate);
        return mode == FilterMode.INCLUDE ? withinRange : !withinRange;
    }

    public static DateRangeFilter of(Instant startDate, Instant endDate, FilterMode mode) {
        return new DateRangeFilter(startDate, endDate, mode);
    }
}
