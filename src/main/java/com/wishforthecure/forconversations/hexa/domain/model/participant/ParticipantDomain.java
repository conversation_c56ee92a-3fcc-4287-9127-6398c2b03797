package com.wishforthecure.forconversations.hexa.domain.model.participant;

import com.wishforthecure.forconversations.hexa.domain.model.alias.AliasDomain;
import java.util.List;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
public class ParticipantDomain {

    private ParticipantDomainId participantDomainId;
    private String name;
    private String surname;
    private String secondSurname;
    private String emailContact;
    private String mobileContact;
    private List<AliasDomain> aliasList;
}
