package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
public class WhatsAppSourceDomain implements SourceDomain {

    SourceDomainId sourceId;
    Instant time;
    List<WhatsAppMessageDomain> messages;
    byte[] source;
    SourceDomainType sourceType = SourceDomainType.WHATSAPP;

    @Override
    public SourceDomainId getSourceId() {
        return sourceId;
    }

    @Override
    public Instant getTime() {
        return time;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MessageDomain> getMessages() {
        return (List<MessageDomain>) (List<?>) messages;
    }

    public List<WhatsAppMessageDomain> getWhatsAppMessages() {
        return messages;
    }

    @Override
    public byte[] getSource() {
        return source;
    }

    public static WhatsAppSourceDomain create(SourceDomainId sourceId, Instant time, List<WhatsAppMessageDomain> messages, byte[] source) {
        return new WhatsAppSourceDomain(sourceId, time, messages, source, SourceDomainType.WHATSAPP);
    }
}
