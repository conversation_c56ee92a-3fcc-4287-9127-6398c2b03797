package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomainType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class EmailMessageDomain implements MessageDomain {

    MessageDomainId messageId;
    Instant time;
    EmailAliasDomain sender;
    List<EmailAliasDomain> recipients;
    String content;
    List<FeelingDomain> feelingDomainList;
    List<TagDomain> tagDomainList;
    SourceDomainType sourceType;

    @Override
    public MessageDomainId messageId() {
        return messageId;
    }

    @Override
    public Instant time() {
        return time;
    }

    @Override
    public String content() {
        return content;
    }

    @Override
    public List<FeelingDomain> feelingList() {
        return feelingDomainList;
    }

    @Override
    public List<TagDomain> tagList() {
        return tagDomainList;
    }

    @Override
    public String getSender() {
        return sender.getValue();
    }

    @Override
    public String getRecipients() {
        return recipients.stream().map(EmailAliasDomain::getValue).collect(Collectors.joining(","));
    }

    public static EmailMessageDomain create(
        MessageDomainId messageId,
        Instant time,
        EmailAliasDomain sender,
        List<EmailAliasDomain> recipients,
        String content,
        List<FeelingDomain> feelingDomainList,
        List<TagDomain> tagDomainList,
        SourceDomainType sourceType
    ) {
        return new EmailMessageDomain(messageId, time, sender, recipients, content, feelingDomainList, tagDomainList, sourceType);
    }
}
