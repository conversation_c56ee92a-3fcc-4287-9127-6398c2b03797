package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;

/**
 * Domain interface for message entities.
 * This interface can be implemented by both Value Objects and Entities.
 */
public interface MessageDomain {
    MessageDomainId messageId();

    Instant time();

    String getSender();

    String getRecipients();

    String content();

    List<FeelingDomain> feelingList();

    List<TagDomain> tagList();
}
