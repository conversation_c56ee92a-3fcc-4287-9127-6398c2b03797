package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class AddressFilter implements MessageFilter {

    Set<EmailAddress> addresses;
    FilterMode mode;

    @Override
    public boolean apply(MessageDomain message) {
        String sender = message.getSender();
        String recipients = message.getRecipients();
        boolean matches = addresses.stream().anyMatch(addr -> sender.contains(addr.getValue()) || recipients.contains(addr.getValue()));
        return mode == FilterMode.INCLUDE ? matches : !matches;
    }

    public static AddressFilter of(Set<String> addresses, FilterMode mode) {
        Set<EmailAddress> emailAddresses = addresses.stream().map(EmailAddress::new).collect(Collectors.toSet());
        return new AddressFilter(emailAddresses, mode);
    }
}
