package com.wishforthecure.forconversations.hexa.domain.model.tag;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
public final class TagDomain {

    TagDomainId id;
    String value;

    private TagDomain(String value) {
        this.value = value;
    }

    public static TagDomain of(String value) {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException("Tag value cannot be blank.");
        }
        return new TagDomain(value.trim().toLowerCase());
    }
}
